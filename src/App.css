/* Ludo Game Styles */

/* Custom animations */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.8);
  }
}

@keyframes bounce-in {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Piece animations */
.piece-enter {
  animation: bounce-in 0.5s ease-out;
}

.piece-movable {
  animation: pulse-glow 2s infinite;
}

/* Board cell hover effects */
.ludo-cell:hover {
  background-color: rgba(59, 130, 246, 0.1);
  transition: background-color 0.2s ease;
}

/* Safe zone styling */
.safe-zone {
  position: relative;
}

.safe-zone::before {
  content: '★';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #fbbf24;
  font-size: 12px;
  z-index: 1;
}

/* Player color utilities */
.bg-red-100 { background-color: #fee2e2; }
.bg-red-200 { background-color: #fecaca; }
.bg-blue-100 { background-color: #dbeafe; }
.bg-blue-200 { background-color: #bfdbfe; }
.bg-green-100 { background-color: #dcfce7; }
.bg-green-200 { background-color: #bbf7d0; }
.bg-yellow-100 { background-color: #fef3c7; }
.bg-yellow-200 { background-color: #fde68a; }

.border-red-400 { border-color: #f87171; }
.border-blue-400 { border-color: #60a5fa; }
.border-green-400 { border-color: #4ade80; }
.border-yellow-400 { border-color: #facc15; }

/* Responsive design */
@media (max-width: 1024px) {
  .ludo-board {
    transform: scale(0.8);
  }
}

@media (max-width: 768px) {
  .ludo-board {
    transform: scale(0.6);
  }

  .player-panels {
    grid-template-columns: 1fr;
  }
}

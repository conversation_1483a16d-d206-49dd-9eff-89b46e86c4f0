import React, { useState } from 'react';
import GameSetup from './components/GameSetup.jsx';
import LudoBoard from './components/Board.jsx';
import Dice from './components/Dice.jsx';
import PlayerPanel from './components/PlayerPanel.jsx';
import GameStatus from './components/GameStatus.jsx';
import { useLudoGame } from './hooks/useLudoGame.js';

function App() {
  const [gameStarted, setGameStarted] = useState(false);
  const [selectedPlayers, setSelectedPlayers] = useState([]);

  const {
    gameState,
    currentPlayer,
    diceValue,
    isRolling,
    validMoves,
    gamePhase,
    handleDiceRoll,
    handlePieceMove,
    resetGame,
    canMovePiece,
    getPieceMove
  } = useLudoGame(selectedPlayers.length);

  const handleStartGame = (players) => {
    setSelectedPlayers(players);
    setGameStarted(true);
  };

  const handleResetGame = () => {
    resetGame();
    setGameStarted(false);
    setSelectedPlayers([]);
  };

  if (!gameStarted) {
    return <GameSetup onStartGame={handleStartGame} />;
  }

  // Helper function to get player positions
  const getPlayerPositions = () => {
    const positions = {
      red: { top: '10px', left: '10px' },      // Top-left
      blue: { top: '10px', right: '10px' },    // Top-right
      green: { bottom: '10px', right: '10px' }, // Bottom-right
      yellow: { bottom: '10px', left: '10px' }  // Bottom-left
    };

    // Filter to only show selected players
    const activePositions = {};
    selectedPlayers.forEach(player => {
      activePositions[player] = positions[player];
    });

    return activePositions;
  };

  const playerPositions = getPlayerPositions();

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-100 to-blue-100 p-4">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-4xl font-bold text-center mb-6 text-gray-800">
          Ludo Game
        </h1>

        {/* Game container with board in center and players in corners */}
        <div className="relative flex justify-center items-center min-h-[600px]">

          {/* Player panels positioned in corners */}
          {Object.entries(playerPositions).map(([player, position]) => (
            <div
              key={player}
              className="absolute z-10"
              style={position}
            >
              <PlayerPanel
                player={player}
                pieces={gameState.pieces[player] || []}
                isCurrentPlayer={player === currentPlayer}
                diceValue={player === currentPlayer ? diceValue : null}
                gamePhase={player === currentPlayer ? gamePhase : null}
                onPieceClick={handlePieceMove}
                canMovePiece={canMovePiece}
                getPieceMove={getPieceMove}
              />
            </div>
          ))}

          {/* Center area with board and controls */}
          <div className="flex flex-col items-center space-y-6">
            {/* Game Status */}
            <GameStatus
              gameState={gameState}
              currentPlayer={currentPlayer}
              gamePhase={gamePhase}
              diceValue={diceValue}
              onResetGame={handleResetGame}
            />

            {/* Game Board */}
            <LudoBoard
              gameState={gameState}
              onPieceClick={handlePieceMove}
              canMovePiece={canMovePiece}
              getPieceMove={getPieceMove}
            />

            {/* Dice and controls */}
            <div className="flex items-center space-x-6">
              <Dice
                value={diceValue}
                isRolling={isRolling}
                onRoll={handleDiceRoll}
                disabled={gamePhase !== 'waiting_for_roll' || gameState.winner}
              />

              {validMoves.length > 0 && (
                <div className="bg-white p-4 rounded-lg shadow-lg">
                  <h3 className="font-semibold mb-2">Valid Moves:</h3>
                  <div className="text-sm text-gray-600">
                    {validMoves.length} piece{validMoves.length !== 1 ? 's' : ''} can move
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;

import React, { useState } from 'react';
import GameSetup from './components/GameSetup.jsx';
import LudoBoard from './components/Board.jsx';
import Dice from './components/Dice.jsx';
import PlayerPanel from './components/PlayerPanel.jsx';
import GameStatus from './components/GameStatus.jsx';
import { useLudoGame } from './hooks/useLudoGame.js';

function App() {
  const [gameStarted, setGameStarted] = useState(false);
  const [selectedPlayers, setSelectedPlayers] = useState([]);

  const {
    gameState,
    currentPlayer,
    diceValue,
    isRolling,
    validMoves,
    gamePhase,
    handleDiceRoll,
    handlePieceMove,
    resetGame,
    canMovePiece,
    getPieceMove
  } = useLudoGame(selectedPlayers.length);

  const handleStartGame = (players) => {
    setSelectedPlayers(players);
    setGameStarted(true);
  };

  const handleResetGame = () => {
    resetGame();
    setGameStarted(false);
    setSelectedPlayers([]);
  };

  if (!gameStarted) {
    return <GameSetup onStartGame={handleStartGame} />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-100 to-blue-100 p-4">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-4xl font-bold text-center mb-6 text-gray-800">
          Ludo Game
        </h1>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left sidebar - Player panels */}
          <div className="space-y-4">
            <GameStatus
              gameState={gameState}
              currentPlayer={currentPlayer}
              gamePhase={gamePhase}
              diceValue={diceValue}
              onResetGame={handleResetGame}
            />

            {selectedPlayers.map(player => (
              <PlayerPanel
                key={player}
                player={player}
                pieces={gameState.pieces[player] || []}
                isCurrentPlayer={player === currentPlayer}
                diceValue={player === currentPlayer ? diceValue : null}
                gamePhase={player === currentPlayer ? gamePhase : null}
              />
            ))}
          </div>

          {/* Center - Game board */}
          <div className="flex justify-center">
            <LudoBoard
              gameState={gameState}
              onPieceClick={handlePieceMove}
              canMovePiece={canMovePiece}
              getPieceMove={getPieceMove}
            />
          </div>

          {/* Right sidebar - Dice and controls */}
          <div className="flex flex-col items-center space-y-6">
            <Dice
              value={diceValue}
              isRolling={isRolling}
              onRoll={handleDiceRoll}
              disabled={gamePhase !== 'waiting_for_roll' || gameState.winner}
            />

            {validMoves.length > 0 && (
              <div className="bg-white p-4 rounded-lg shadow-lg">
                <h3 className="font-semibold mb-2">Valid Moves:</h3>
                <div className="text-sm text-gray-600">
                  {validMoves.length} piece{validMoves.length !== 1 ? 's' : ''} can move
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;

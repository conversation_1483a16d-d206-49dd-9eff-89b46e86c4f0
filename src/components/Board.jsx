import React from 'react';
import LudoCell from './LudoCell.jsx';
import HomeArea from './HomeArea.jsx';
import { BOARD_SIZE } from '../constants/ludoConstants.js';

const LudoBoard = ({
  gameState,
  onPieceClick,
  canMovePiece,
  getPieceMove
}) => {
  // Get pieces at each position
  const getPiecesAtPosition = (row, col) => {
    const pieces = [];
    Object.values(gameState.pieces).forEach(playerPieces => {
      playerPieces.forEach(piece => {
        if (piece.position && piece.position[0] === row && piece.position[1] === col) {
          pieces.push(piece);
        }
      });
    });
    return pieces;
  };

  const generateGrid = () => {
    const grid = [];
    for (let row = 0; row < BOARD_SIZE; row++) {
      const rowCells = [];
      for (let col = 0; col < BOARD_SIZE; col++) {
        const pieces = getPiecesAtPosition(row, col);
        rowCells.push(
          <LudoCell
            key={`${row}-${col}`}
            row={row}
            col={col}
            pieces={pieces}
            onPieceClick={onPieceClick}
            canMovePiece={canMovePiece}
            getPieceMove={getPieceMove}
          />
        );
      }
      grid.push(
        <div key={row} className="flex">
          {rowCells}
        </div>
      );
    }
    return grid;
  };

  return (
    <div className="relative">
      {/* Main board grid */}
      <div className="inline-block border-4 border-gray-800 bg-white">
        {generateGrid()}
      </div>

      {/* Home areas positioned around the board */}
      <div className="absolute top-2 left-2">
        <HomeArea
          player="red"
          pieces={gameState.pieces.red || []}
          onPieceClick={onPieceClick}
          canMovePiece={canMovePiece}
          getPieceMove={getPieceMove}
        />
      </div>

      <div className="absolute top-2 right-2">
        <HomeArea
          player="blue"
          pieces={gameState.pieces.blue || []}
          onPieceClick={onPieceClick}
          canMovePiece={canMovePiece}
          getPieceMove={getPieceMove}
        />
      </div>

      <div className="absolute bottom-2 right-2">
        <HomeArea
          player="green"
          pieces={gameState.pieces.green || []}
          onPieceClick={onPieceClick}
          canMovePiece={canMovePiece}
          getPieceMove={getPieceMove}
        />
      </div>

      <div className="absolute bottom-2 left-2">
        <HomeArea
          player="yellow"
          pieces={gameState.pieces.yellow || []}
          onPieceClick={onPieceClick}
          canMovePiece={canMovePiece}
          getPieceMove={getPieceMove}
        />
      </div>
    </div>
  );
};

export default LudoBoard;

import React from "react";
import Cell from "./Cell";

const generateGrid = () => {
  let grid = [];
  for (let row = 0; row < 15; row++) {
    let rowCells = [];
    for (let col = 0; col < 15; col++) {
      rowCells.push(<Cell key={`${row}-${col}`} row={row} col={col} />);
    }
    grid.push(
      <div key={row} className="flex">
        {rowCells}
      </div>
    );
  }
  return grid;
};

export default function Board() {
  return <div className="grid">{generateGrid()}</div>;
}

import React from 'react';
import LudoCell from './LudoCell.jsx';
import { BOARD_SIZE } from '../constants/ludoConstants.js';

const LudoBoard = ({
  gameState,
  onPieceClick,
  canMovePiece,
  getPieceMove
}) => {
  // Get pieces at each position
  const getPiecesAtPosition = (row, col) => {
    const pieces = [];
    Object.values(gameState.pieces).forEach(playerPieces => {
      playerPieces.forEach(piece => {
        if (piece.position && piece.position[0] === row && piece.position[1] === col) {
          pieces.push(piece);
        }
      });
    });
    return pieces;
  };

  const generateGrid = () => {
    const grid = [];
    for (let row = 0; row < BOARD_SIZE; row++) {
      const rowCells = [];
      for (let col = 0; col < BOARD_SIZE; col++) {
        const pieces = getPiecesAtPosition(row, col);
        rowCells.push(
          <LudoCell
            key={`${row}-${col}`}
            row={row}
            col={col}
            pieces={pieces}
            onPieceClick={onPieceClick}
            canMovePiece={canMovePiece}
            getPieceMove={getPieceMove}
          />
        );
      }
      grid.push(
        <div key={row} className="flex">
          {rowCells}
        </div>
      );
    }
    return grid;
  };

  return (
    <div className="inline-block border-4 border-gray-800 bg-white shadow-2xl rounded-lg overflow-hidden">
      {generateGrid()}
    </div>
  );
};

export default LudoBoard;

import React from 'react';

const Dice = ({ value, isRolling, onRoll, disabled = false }) => {
  const getDiceFace = (num) => {
    const dots = {
      1: [4],
      2: [0, 8],
      3: [0, 4, 8],
      4: [0, 2, 6, 8],
      5: [0, 2, 4, 6, 8],
      6: [0, 2, 3, 5, 6, 8]
    };

    return (
      <div className="grid grid-cols-3 gap-1 w-12 h-12 p-2">
        {Array.from({ length: 9 }, (_, i) => (
          <div
            key={i}
            className={`
              w-2 h-2 rounded-full
              ${dots[num]?.includes(i) ? 'bg-gray-800' : 'bg-transparent'}
            `}
          />
        ))}
      </div>
    );
  };

  return (
    <div className="flex flex-col items-center space-y-4">
      <div
        className={`
          bg-white
          border-2
          border-gray-800
          rounded-lg
          shadow-lg
          cursor-pointer
          transition-all
          duration-200
          ${isRolling ? 'animate-spin' : ''}
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:shadow-xl hover:scale-105'}
        `}
        onClick={!disabled && !isRolling ? onRoll : undefined}
      >
        {isRolling ? (
          <div className="w-12 h-12 flex items-center justify-center">
            <div className="w-6 h-6 border-2 border-gray-800 border-t-transparent rounded-full animate-spin"></div>
          </div>
        ) : value ? (
          getDiceFace(value)
        ) : (
          <div className="w-12 h-12 flex items-center justify-center text-gray-500 font-bold">
            ?
          </div>
        )}
      </div>
      
      <button
        onClick={onRoll}
        disabled={disabled || isRolling}
        className={`
          px-4 py-2
          bg-blue-500
          text-white
          rounded-lg
          font-semibold
          transition-all
          duration-200
          ${disabled || isRolling 
            ? 'opacity-50 cursor-not-allowed' 
            : 'hover:bg-blue-600 hover:shadow-lg'
          }
        `}
      >
        {isRolling ? 'Rolling...' : 'Roll Dice'}
      </button>
    </div>
  );
};

export default Dice;

import React, { useState } from 'react';
import { PLAYER_COLORS } from '../constants/ludoConstants.js';

const GameSetup = ({ onStartGame }) => {
  const [playerCount, setPlayerCount] = useState(4);
  const [selectedPlayers, setSelectedPlayers] = useState(['red', 'blue', 'green', 'yellow']);

  const allPlayers = ['red', 'blue', 'green', 'yellow'];

  const handlePlayerCountChange = (count) => {
    setPlayerCount(count);
    if (count === 2) {
      // For 2 players, use opposite corners (red and green)
      setSelectedPlayers(['red', 'green']);
    } else if (count === 3) {
      // For 3 players, use red, blue, green (skip yellow)
      setSelectedPlayers(['red', 'blue', 'green']);
    } else {
      // For 4 players, use all
      setSelectedPlayers(allPlayers.slice(0, count));
    }
  };

  const handlePlayerToggle = (player) => {
    if (selectedPlayers.includes(player)) {
      if (selectedPlayers.length > 2) {
        setSelectedPlayers(selectedPlayers.filter(p => p !== player));
        setPlayerCount(selectedPlayers.length - 1);
      }
    } else {
      if (selectedPlayers.length < 4) {
        setSelectedPlayers([...selectedPlayers, player]);
        setPlayerCount(selectedPlayers.length + 1);
      }
    }
  };

  const handleStartGame = () => {
    onStartGame(selectedPlayers);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-100 to-green-100 flex items-center justify-center p-4">
      <div className="bg-white rounded-xl shadow-2xl p-8 max-w-md w-full">
        <h1 className="text-3xl font-bold text-center mb-8 text-gray-800">
          Ludo Game Setup
        </h1>

        <div className="mb-6">
          <h2 className="text-lg font-semibold mb-4 text-gray-700">
            Number of Players
          </h2>
          <div className="flex space-x-2">
            {[2, 3, 4].map(count => (
              <button
                key={count}
                onClick={() => handlePlayerCountChange(count)}
                className={`
                  px-4 py-2 rounded-lg font-semibold transition-all duration-200
                  ${playerCount === count
                    ? 'bg-blue-500 text-white shadow-lg'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }
                `}
              >
                {count} Players
              </button>
            ))}
          </div>
        </div>

        <div className="mb-8">
          <h2 className="text-lg font-semibold mb-4 text-gray-700">
            Select Players
          </h2>
          <div className="grid grid-cols-2 gap-3">
            {allPlayers.map(player => (
              <button
                key={player}
                onClick={() => handlePlayerToggle(player)}
                disabled={!selectedPlayers.includes(player) && selectedPlayers.length >= 4}
                className={`
                  p-3 rounded-lg border-2 transition-all duration-200 flex items-center space-x-2
                  ${selectedPlayers.includes(player)
                    ? 'border-gray-800 bg-gray-100 shadow-md'
                    : 'border-gray-300 bg-white hover:border-gray-400'
                  }
                  ${!selectedPlayers.includes(player) && selectedPlayers.length >= 4
                    ? 'opacity-50 cursor-not-allowed'
                    : 'cursor-pointer'
                  }
                `}
              >
                <div
                  className="w-6 h-6 rounded-full border-2 border-gray-800"
                  style={{ backgroundColor: PLAYER_COLORS[player] }}
                />
                <span className="font-medium capitalize">{player}</span>
                {selectedPlayers.includes(player) && (
                  <span className="ml-auto text-green-600">✓</span>
                )}
              </button>
            ))}
          </div>
          <p className="text-sm text-gray-500 mt-2">
            Select {playerCount} players (minimum 2, maximum 4)
          </p>
        </div>

        <button
          onClick={handleStartGame}
          disabled={selectedPlayers.length < 2}
          className={`
            w-full py-3 rounded-lg font-bold text-lg transition-all duration-200
            ${selectedPlayers.length >= 2
              ? 'bg-green-500 text-white hover:bg-green-600 shadow-lg hover:shadow-xl'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }
          `}
        >
          Start Game
        </button>

        <div className="mt-6 text-center text-sm text-gray-600">
          <h3 className="font-semibold mb-2">How to Play:</h3>
          <ul className="text-left space-y-1">
            <li>• Roll 6 to move pieces out of home</li>
            <li>• Land on opponents to send them home</li>
            <li>• Safe spots protect your pieces</li>
            <li>• Get all pieces to the center to win!</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default GameSetup;

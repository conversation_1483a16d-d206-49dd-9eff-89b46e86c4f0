import React from 'react';
import { PLAYER_COLORS } from '../constants/ludoConstants.js';

const GameStatus = ({ 
  gameState, 
  currentPlayer, 
  gamePhase, 
  diceValue,
  onResetGame 
}) => {
  if (gameState.winner) {
    return (
      <div className="text-center p-6 bg-white rounded-lg shadow-lg border-4 border-yellow-400">
        <h2 className="text-3xl font-bold mb-4 text-gray-800">
          🎉 Game Over! 🎉
        </h2>
        <div className="flex items-center justify-center space-x-3 mb-4">
          <div
            className="w-8 h-8 rounded-full border-2 border-gray-800"
            style={{ backgroundColor: PLAYER_COLORS[gameState.winner] }}
          />
          <span className="text-2xl font-bold capitalize">
            {gameState.winner} Wins!
          </span>
        </div>
        <button
          onClick={onResetGame}
          className="px-6 py-3 bg-blue-500 text-white rounded-lg font-semibold hover:bg-blue-600 transition-colors duration-200"
        >
          Play Again
        </button>
      </div>
    );
  }

  return (
    <div className="text-center p-4 bg-white rounded-lg shadow-lg">
      <h2 className="text-xl font-bold mb-3 text-gray-800">
        Game Status
      </h2>
      
      <div className="flex items-center justify-center space-x-3 mb-3">
        <div
          className="w-6 h-6 rounded-full border-2 border-gray-800"
          style={{ backgroundColor: PLAYER_COLORS[currentPlayer] }}
        />
        <span className="text-lg font-semibold capitalize">
          {currentPlayer}'s Turn
        </span>
      </div>

      {diceValue && (
        <div className="mb-3">
          <span className="text-sm text-gray-600">Last Roll: </span>
          <span className="text-xl font-bold">{diceValue}</span>
        </div>
      )}

      <div className="text-sm text-gray-600">
        {gamePhase === 'waiting_for_roll' && 'Click the dice to roll'}
        {gamePhase === 'waiting_for_move' && 'Select a piece to move'}
      </div>

      <button
        onClick={onResetGame}
        className="mt-4 px-4 py-2 bg-gray-500 text-white rounded-lg text-sm hover:bg-gray-600 transition-colors duration-200"
      >
        Reset Game
      </button>
    </div>
  );
};

export default GameStatus;

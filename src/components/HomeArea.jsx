import React from 'react';
import Piece from './Piece.jsx';
import { HOME_POSITIONS, PLAYER_COLORS } from '../constants/ludoConstants.js';

const HomeArea = ({ 
  player, 
  pieces, 
  onPieceClick, 
  canMovePiece, 
  getPieceMove 
}) => {
  const homePositions = HOME_POSITIONS[player];
  const homePieces = pieces.filter(piece => piece.state === 'home');
  const playerColor = PLAYER_COLORS[player];

  return (
    <div className="relative">
      {/* Home area background */}
      <div 
        className="w-24 h-24 rounded-lg border-4 border-gray-800 p-2"
        style={{ backgroundColor: `${playerColor}40` }}
      >
        <div className="grid grid-cols-2 gap-2 h-full">
          {homePositions.map((position, index) => {
            const piece = homePieces[index];
            return (
              <div
                key={`home-${player}-${index}`}
                className="w-8 h-8 rounded-full border-2 border-gray-600 bg-white flex items-center justify-center"
              >
                {piece && (
                  <Piece
                    piece={piece}
                    canMove={canMovePiece(piece.player, piece.id.split('-')[1])}
                    onClick={() => {
                      const move = getPieceMove(parseInt(piece.id.split('-')[1]));
                      if (move) onPieceClick(move);
                    }}
                    size="normal"
                  />
                )}
              </div>
            );
          })}
        </div>
      </div>
      
      {/* Player label */}
      <div 
        className="absolute -bottom-6 left-0 right-0 text-center font-bold text-sm capitalize"
        style={{ color: playerColor }}
      >
        {player}
      </div>
    </div>
  );
};

export default HomeArea;

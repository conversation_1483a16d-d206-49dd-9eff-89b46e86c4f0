import React from 'react';
import Piece from './Piece.jsx';
import { 
  SAFE_POSITIONS, 
  START_POSITIONS, 
  HOME_POSITIONS,
  PLAYER_COLORS 
} from '../constants/ludoConstants.js';

const LudoCell = ({ 
  row, 
  col, 
  pieces = [], 
  onPieceClick,
  canMovePiece,
  getPieceMove 
}) => {
  // Check cell type
  const isSafe = SAFE_POSITIONS.some(([r, c]) => r === row && c === col);
  const isStart = Object.values(START_POSITIONS).some(([r, c]) => r === row && c === col);
  const isCenter = row === 7 && col === 7;
  
  // Check if it's a home area
  let homePlayer = null;
  Object.entries(HOME_POSITIONS).forEach(([player, positions]) => {
    if (positions.some(([r, c]) => r === row && c === col)) {
      homePlayer = player;
    }
  });

  // Check if it's part of colored paths
  let pathColor = null;
  if ((row === 7 && col >= 1 && col <= 6) || (row === 6 && col === 7)) {
    pathColor = 'red';
  } else if ((col === 7 && row >= 1 && row <= 6) || (row === 7 && col === 6)) {
    pathColor = 'blue';
  } else if ((row === 7 && col >= 8 && col <= 13) || (row === 8 && col === 7)) {
    pathColor = 'green';
  } else if ((col === 7 && row >= 8 && row <= 13) || (row === 7 && col === 8)) {
    pathColor = 'yellow';
  }

  const getCellStyle = () => {
    let baseStyle = 'w-8 h-8 border-2 border-gray-600 flex items-center justify-center relative';
    
    if (isCenter) {
      return `${baseStyle} bg-gradient-to-br from-red-400 via-blue-400 via-green-400 to-yellow-400`;
    }
    
    if (homePlayer) {
      return `${baseStyle} bg-${homePlayer}-200 border-${homePlayer}-400`;
    }
    
    if (pathColor) {
      return `${baseStyle} bg-${pathColor}-100`;
    }
    
    if (isSafe) {
      return `${baseStyle} bg-yellow-200 relative`;
    }
    
    if (isStart) {
      return `${baseStyle} bg-gray-200 border-2 border-gray-600`;
    }
    
    return `${baseStyle} bg-white`;
  };

  const renderPieces = () => {
    if (pieces.length === 0) return null;
    
    if (pieces.length === 1) {
      const piece = pieces[0];
      return (
        <Piece
          piece={piece}
          canMove={canMovePiece(piece.player, piece.id.split('-')[1])}
          onClick={() => {
            const move = getPieceMove(parseInt(piece.id.split('-')[1]));
            if (move) onPieceClick(move);
          }}
          size="normal"
        />
      );
    }
    
    // Multiple pieces - stack them
    return (
      <div className="relative">
        {pieces.map((piece, index) => (
          <div
            key={piece.id}
            className="absolute"
            style={{
              top: `${index * 2}px`,
              left: `${index * 2}px`,
              zIndex: index
            }}
          >
            <Piece
              piece={piece}
              canMove={canMovePiece(piece.player, piece.id.split('-')[1])}
              onClick={() => {
                const move = getPieceMove(parseInt(piece.id.split('-')[1]));
                if (move) onPieceClick(move);
              }}
              size="small"
            />
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className={getCellStyle()}>
      {isSafe && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-3 h-3 bg-yellow-400 rotate-45"></div>
        </div>
      )}
      {renderPieces()}
    </div>
  );
};

export default LudoCell;

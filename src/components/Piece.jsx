import React from 'react';
import { PLAYER_COLORS } from '../constants/ludoConstants.js';

const Piece = ({ 
  piece, 
  onClick, 
  canMove = false, 
  isHighlighted = false,
  size = 'normal' 
}) => {
  const color = PLAYER_COLORS[piece.player];
  
  const sizeClasses = {
    small: 'w-4 h-4',
    normal: 'w-6 h-6',
    large: 'w-8 h-8'
  };

  const handleClick = () => {
    if (canMove && onClick) {
      onClick(piece);
    }
  };

  return (
    <div
      className={`
        ${sizeClasses[size]}
        rounded-full
        border-2
        border-gray-800
        cursor-pointer
        transition-all
        duration-200
        flex
        items-center
        justify-center
        font-bold
        text-xs
        ${canMove ? 'hover:scale-110 hover:shadow-lg' : ''}
        ${isHighlighted ? 'ring-4 ring-yellow-400 ring-opacity-75' : ''}
        ${canMove ? 'animate-pulse' : ''}
      `}
      style={{ 
        backgroundColor: color,
        color: piece.player === 'yellow' ? '#000' : '#fff'
      }}
      onClick={handleClick}
      title={`${piece.player} piece ${piece.id}`}
    >
      {piece.id.split('-')[1]}
    </div>
  );
};

export default Piece;

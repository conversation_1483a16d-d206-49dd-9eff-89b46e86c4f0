import React from 'react';
import { PLAYER_COLORS, PIECE_STATES } from '../constants/ludoConstants.js';

const PlayerPanel = ({ 
  player, 
  pieces, 
  isCurrentPlayer, 
  diceValue, 
  gamePhase 
}) => {
  const playerColor = PLAYER_COLORS[player];
  
  const pieceStats = {
    home: pieces.filter(p => p.state === PIECE_STATES.HOME).length,
    onTrack: pieces.filter(p => p.state === PIECE_STATES.ON_TRACK).length,
    inHomeStretch: pieces.filter(p => p.state === PIECE_STATES.IN_HOME_STRETCH).length,
    finished: pieces.filter(p => p.state === PIECE_STATES.FINISHED).length
  };

  return (
    <div 
      className={`
        p-4 rounded-lg border-2 transition-all duration-300
        ${isCurrentPlayer 
          ? 'border-yellow-400 bg-yellow-50 shadow-lg' 
          : 'border-gray-300 bg-gray-50'
        }
      `}
    >
      <div className="flex items-center space-x-3 mb-3">
        <div 
          className="w-6 h-6 rounded-full border-2 border-gray-800"
          style={{ backgroundColor: playerColor }}
        />
        <h3 className="font-bold text-lg capitalize">{player}</h3>
        {isCurrentPlayer && (
          <span className="px-2 py-1 bg-yellow-400 text-yellow-900 rounded-full text-xs font-semibold">
            Current Turn
          </span>
        )}
      </div>

      <div className="grid grid-cols-2 gap-2 text-sm">
        <div className="flex justify-between">
          <span>Home:</span>
          <span className="font-semibold">{pieceStats.home}</span>
        </div>
        <div className="flex justify-between">
          <span>On Track:</span>
          <span className="font-semibold">{pieceStats.onTrack}</span>
        </div>
        <div className="flex justify-between">
          <span>Home Stretch:</span>
          <span className="font-semibold">{pieceStats.inHomeStretch}</span>
        </div>
        <div className="flex justify-between">
          <span>Finished:</span>
          <span className="font-semibold text-green-600">{pieceStats.finished}</span>
        </div>
      </div>

      {isCurrentPlayer && diceValue && (
        <div className="mt-3 p-2 bg-white rounded border">
          <div className="text-center">
            <span className="text-sm text-gray-600">Last Roll:</span>
            <div className="text-2xl font-bold">{diceValue}</div>
          </div>
        </div>
      )}

      {isCurrentPlayer && (
        <div className="mt-2 text-xs text-center">
          {gamePhase === 'waiting_for_roll' && (
            <span className="text-blue-600">Click dice to roll</span>
          )}
          {gamePhase === 'waiting_for_move' && (
            <span className="text-green-600">Select a piece to move</span>
          )}
        </div>
      )}
    </div>
  );
};

export default PlayerPanel;

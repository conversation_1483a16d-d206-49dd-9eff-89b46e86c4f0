import React from 'react';
import Piece from './Piece.jsx';
import { PLAYER_COLORS, PIECE_STATES } from '../constants/ludoConstants.js';

const PlayerPanel = ({
  player,
  pieces,
  isCurrentPlayer,
  diceValue,
  gamePhase,
  onPieceClick,
  canMovePiece,
  getPieceMove
}) => {
  const playerColor = PLAYER_COLORS[player];

  const pieceStats = {
    home: pieces.filter(p => p.state === PIECE_STATES.HOME).length,
    onTrack: pieces.filter(p => p.state === PIECE_STATES.ON_TRACK).length,
    inHomeStretch: pieces.filter(p => p.state === PIECE_STATES.IN_HOME_STRETCH).length,
    finished: pieces.filter(p => p.state === PIECE_STATES.FINISHED).length
  };

  const homePieces = pieces.filter(piece => piece.state === PIECE_STATES.HOME);

  return (
    <div
      className={`
        p-3 rounded-lg border-2 transition-all duration-300 w-48 bg-white shadow-lg
        ${isCurrentPlayer
          ? 'border-yellow-400 ring-2 ring-yellow-200'
          : 'border-gray-300'
        }
      `}
    >
      <div className="flex items-center space-x-2 mb-3">
        <div
          className="w-5 h-5 rounded-full border-2 border-gray-800"
          style={{ backgroundColor: playerColor }}
        />
        <h3 className="font-bold text-sm capitalize">{player}</h3>
        {isCurrentPlayer && (
          <span className="px-1 py-0.5 bg-yellow-400 text-yellow-900 rounded text-xs font-semibold">
            Turn
          </span>
        )}
      </div>

      <div className="grid grid-cols-2 gap-1 text-xs">
        <div className="flex justify-between">
          <span>Home:</span>
          <span className="font-semibold">{pieceStats.home}</span>
        </div>
        <div className="flex justify-between">
          <span>Track:</span>
          <span className="font-semibold">{pieceStats.onTrack}</span>
        </div>
        <div className="flex justify-between">
          <span>Stretch:</span>
          <span className="font-semibold">{pieceStats.inHomeStretch}</span>
        </div>
        <div className="flex justify-between">
          <span>Done:</span>
          <span className="font-semibold text-green-600">{pieceStats.finished}</span>
        </div>
      </div>

      {isCurrentPlayer && diceValue && (
        <div className="mt-2 p-2 bg-gray-100 rounded border">
          <div className="text-center">
            <span className="text-xs text-gray-600">Roll:</span>
            <div className="text-lg font-bold">{diceValue}</div>
          </div>
        </div>
      )}

      {/* Home pieces area */}
      {homePieces.length > 0 && (
        <div className="mt-2">
          <div className="text-xs text-gray-600 mb-1">Home:</div>
          <div className="grid grid-cols-4 gap-1">
            {homePieces.map((piece, index) => (
              <div key={piece.id} className="flex justify-center">
                <Piece
                  piece={piece}
                  canMove={canMovePiece && canMovePiece(piece.player, piece.id.split('-')[1])}
                  onClick={() => {
                    if (onPieceClick && getPieceMove) {
                      const move = getPieceMove(parseInt(piece.id.split('-')[1]));
                      if (move) onPieceClick(move);
                    }
                  }}
                  size="small"
                />
              </div>
            ))}
          </div>
        </div>
      )}

      {isCurrentPlayer && (
        <div className="mt-2 text-xs text-center">
          {gamePhase === 'waiting_for_roll' && (
            <span className="text-blue-600">Click dice to roll</span>
          )}
          {gamePhase === 'waiting_for_move' && (
            <span className="text-green-600">Select a piece to move</span>
          )}
        </div>
      )}
    </div>
  );
};

export default PlayerPanel;

// Ludo game constants and configurations

export const BOARD_SIZE = 15;
export const PLAYERS = {
  RED: 'red',
  BLUE: 'blue', 
  GREEN: 'green',
  YELLOW: 'yellow'
};

export const PLAYER_COLORS = {
  [PLAYERS.RED]: '#ef4444',
  [PLAYERS.BLUE]: '#3b82f6',
  [PLAYERS.GREEN]: '#22c55e',
  [PLAYERS.YELLOW]: '#eab308'
};

export const PIECES_PER_PLAYER = 4;

// Board layout constants
export const HOME_POSITIONS = {
  [PLAYERS.RED]: [
    [1, 1], [1, 2], [2, 1], [2, 2]
  ],
  [PLAYERS.BLUE]: [
    [1, 12], [1, 13], [2, 12], [2, 13]
  ],
  [PLAYERS.GREEN]: [
    [12, 12], [12, 13], [13, 12], [13, 13]
  ],
  [PLAYERS.YELLOW]: [
    [12, 1], [12, 2], [13, 1], [13, 2]
  ]
};

// Starting positions on the main track
export const START_POSITIONS = {
  [PLAYERS.RED]: [6, 1],
  [PLAYERS.BLUE]: [1, 8],
  [PLAYERS.GREEN]: [8, 13],
  [PLAYERS.YELLOW]: [13, 6]
};

// Safe positions on the board (star positions)
export const SAFE_POSITIONS = [
  [6, 2], [2, 6], [6, 8], [8, 6], [6, 12], [12, 8], [8, 12], [12, 6]
];

// Main track path (clockwise around the board)
export const MAIN_TRACK = [
  // Red to Blue
  [6, 1], [6, 2], [6, 3], [6, 4], [6, 5],
  [5, 6], [4, 6], [3, 6], [2, 6], [1, 6],
  [0, 6], [0, 7], [0, 8],
  // Blue to Green  
  [1, 8], [2, 8], [3, 8], [4, 8], [5, 8],
  [6, 9], [6, 10], [6, 11], [6, 12], [6, 13],
  [6, 14], [7, 14], [8, 14],
  // Green to Yellow
  [8, 13], [8, 12], [8, 11], [8, 10], [8, 9],
  [9, 8], [10, 8], [11, 8], [12, 8], [13, 8],
  [14, 8], [14, 7], [14, 6],
  // Yellow to Red
  [13, 6], [12, 6], [11, 6], [10, 6], [9, 6],
  [8, 5], [8, 4], [8, 3], [8, 2], [8, 1],
  [8, 0], [7, 0], [6, 0]
];

// Home stretch paths for each player
export const HOME_STRETCH = {
  [PLAYERS.RED]: [
    [7, 1], [7, 2], [7, 3], [7, 4], [7, 5], [7, 6]
  ],
  [PLAYERS.BLUE]: [
    [1, 7], [2, 7], [3, 7], [4, 7], [5, 7], [6, 7]
  ],
  [PLAYERS.GREEN]: [
    [7, 13], [7, 12], [7, 11], [7, 10], [7, 9], [7, 8]
  ],
  [PLAYERS.YELLOW]: [
    [13, 7], [12, 7], [11, 7], [10, 7], [9, 7], [8, 7]
  ]
};

// Game states
export const GAME_STATES = {
  SETUP: 'setup',
  PLAYING: 'playing',
  FINISHED: 'finished'
};

// Piece states
export const PIECE_STATES = {
  HOME: 'home',
  ON_TRACK: 'on_track',
  IN_HOME_STRETCH: 'in_home_stretch',
  FINISHED: 'finished'
};

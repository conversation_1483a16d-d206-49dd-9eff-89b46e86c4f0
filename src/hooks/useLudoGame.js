import { useState, useCallback } from 'react';
import { 
  initializeGame, 
  rollDice, 
  getValidMoves, 
  executeMove, 
  checkWinner, 
  getAnotherTurn, 
  getNextPlayer 
} from '../utils/ludoLogic.js';

export const useLudoGame = (playerCount = 4) => {
  const [gameState, setGameState] = useState(() => initializeGame(playerCount));
  const [diceValue, setDiceValue] = useState(null);
  const [isRolling, setIsRolling] = useState(false);
  const [validMoves, setValidMoves] = useState([]);
  const [gamePhase, setGamePhase] = useState('waiting_for_roll'); // 'waiting_for_roll', 'waiting_for_move', 'game_over'

  const currentPlayer = gameState.players[gameState.currentPlayerIndex];

  const handleDiceRoll = useCallback(() => {
    if (isRolling || gamePhase !== 'waiting_for_roll') return;

    setIsRolling(true);
    
    // Simulate dice rolling animation
    setTimeout(() => {
      const roll = rollDice();
      setDiceValue(roll);
      setIsRolling(false);

      // Get valid moves for current player
      const moves = getValidMoves(gameState, currentPlayer, roll);
      console.log('Valid moves generated:', {
        player: currentPlayer,
        roll,
        moves,
        gameState: gameState.pieces[currentPlayer]
      });
      setValidMoves(moves);

      if (moves.length === 0) {
        // No valid moves, next player's turn
        setTimeout(() => {
          setGameState(getNextPlayer(gameState));
          setDiceValue(null);
          setGamePhase('waiting_for_roll');
        }, 1500);
      } else {
        setGamePhase('waiting_for_move');
      }
    }, 1000);
  }, [gameState, currentPlayer, isRolling, gamePhase]);

  const handlePieceMove = useCallback((move) => {
    if (gamePhase !== 'waiting_for_move') return;

    const newGameState = executeMove(gameState, currentPlayer, move);
    
    // Check if current player won
    if (checkWinner(newGameState, currentPlayer)) {
      setGameState({
        ...newGameState,
        winner: currentPlayer,
        gamePhase: 'finished'
      });
      setGamePhase('game_over');
      return;
    }

    // Check if player gets another turn
    const anotherTurn = getAnotherTurn(diceValue, false); // TODO: implement capture detection
    
    if (anotherTurn) {
      setGameState(newGameState);
      setDiceValue(null);
      setValidMoves([]);
      setGamePhase('waiting_for_roll');
    } else {
      const nextGameState = getNextPlayer(newGameState);
      setGameState(nextGameState);
      setDiceValue(null);
      setValidMoves([]);
      setGamePhase('waiting_for_roll');
    }
  }, [gameState, currentPlayer, diceValue, gamePhase]);

  const resetGame = useCallback(() => {
    setGameState(initializeGame(playerCount));
    setDiceValue(null);
    setIsRolling(false);
    setValidMoves([]);
    setGamePhase('waiting_for_roll');
  }, [playerCount]);

  const canMovePiece = useCallback((player, pieceIndex) => {
    if (player !== currentPlayer || gamePhase !== 'waiting_for_move') return false;
    const numericIndex = parseInt(pieceIndex);
    const canMove = validMoves.some(move => move.pieceIndex === numericIndex);
    console.log('canMovePiece check:', {
      player,
      pieceIndex,
      numericIndex,
      currentPlayer,
      gamePhase,
      validMoves,
      canMove
    });
    return canMove;
  }, [currentPlayer, gamePhase, validMoves]);

  const getPieceMove = useCallback((pieceIndex) => {
    const numericIndex = parseInt(pieceIndex);
    return validMoves.find(move => move.pieceIndex === numericIndex);
  }, [validMoves]);

  return {
    gameState,
    currentPlayer,
    diceValue,
    isRolling,
    validMoves,
    gamePhase,
    handleDiceRoll,
    handlePieceMove,
    resetGame,
    canMovePiece,
    getPieceMove
  };
};

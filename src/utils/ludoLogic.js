import { 
  MAIN_TRACK, 
  HOME_STRETCH, 
  START_POSITIONS, 
  SAFE_POSITIONS,
  PIECE_STATES,
  PIECES_PER_PLAYER 
} from '../constants/ludoConstants.js';

// Initialize game state
export const initializeGame = (playerCount) => {
  const players = ['red', 'blue', 'green', 'yellow'].slice(0, playerCount);
  
  const gameState = {
    players,
    currentPlayerIndex: 0,
    gamePhase: 'playing',
    winner: null,
    pieces: {}
  };

  // Initialize pieces for each player
  players.forEach(player => {
    gameState.pieces[player] = [];
    for (let i = 0; i < PIECES_PER_PLAYER; i++) {
      gameState.pieces[player].push({
        id: `${player}-${i}`,
        player,
        state: PIECE_STATES.HOME,
        position: null,
        trackIndex: -1
      });
    }
  });

  return gameState;
};

// Roll dice (1-6)
export const rollDice = () => Math.floor(Math.random() * 6) + 1;

// Check if a position is safe
export const isSafePosition = (row, col) => {
  return SAFE_POSITIONS.some(([r, c]) => r === row && c === col);
};

// Get position from track index for a player
export const getPositionFromTrackIndex = (player, trackIndex) => {
  if (trackIndex < 0) return null;
  
  const startPos = START_POSITIONS[player];
  const startTrackIndex = MAIN_TRACK.findIndex(([r, c]) => r === startPos[0] && c === startPos[1]);
  
  if (trackIndex < 52) { // On main track
    const actualIndex = (startTrackIndex + trackIndex) % MAIN_TRACK.length;
    return MAIN_TRACK[actualIndex];
  } else { // In home stretch
    const homeStretchIndex = trackIndex - 52;
    const homeStretch = HOME_STRETCH[player];
    if (homeStretchIndex < homeStretch.length) {
      return homeStretch[homeStretchIndex];
    }
  }
  
  return null;
};

// Get valid moves for a player given dice roll
export const getValidMoves = (gameState, player, diceRoll) => {
  const pieces = gameState.pieces[player];
  const validMoves = [];

  pieces.forEach((piece, index) => {
    if (piece.state === PIECE_STATES.FINISHED) return;

    // Piece in home - can only move out with 6
    if (piece.state === PIECE_STATES.HOME && diceRoll === 6) {
      validMoves.push({
        pieceIndex: index,
        type: 'move_out',
        newPosition: START_POSITIONS[player],
        newTrackIndex: 0
      });
      return;
    }

    // Piece on track or in home stretch
    if (piece.state === PIECE_STATES.ON_TRACK || piece.state === PIECE_STATES.IN_HOME_STRETCH) {
      const newTrackIndex = piece.trackIndex + diceRoll;
      
      // Check if piece reaches finish
      if (newTrackIndex === 57) {
        validMoves.push({
          pieceIndex: index,
          type: 'finish',
          newPosition: null,
          newTrackIndex: 57
        });
        return;
      }

      // Check if move is valid (not beyond finish)
      if (newTrackIndex < 58) {
        const newPosition = getPositionFromTrackIndex(player, newTrackIndex);
        if (newPosition) {
          // Check if position is occupied by own piece
          const isOccupiedByOwn = pieces.some(p => 
            p.position && p.position[0] === newPosition[0] && p.position[1] === newPosition[1]
          );
          
          if (!isOccupiedByOwn) {
            validMoves.push({
              pieceIndex: index,
              type: 'move',
              newPosition,
              newTrackIndex
            });
          }
        }
      }
    }
  });

  return validMoves;
};

// Execute a move
export const executeMove = (gameState, player, move) => {
  const newGameState = JSON.parse(JSON.stringify(gameState));
  const piece = newGameState.pieces[player][move.pieceIndex];
  
  if (move.type === 'move_out') {
    piece.state = PIECE_STATES.ON_TRACK;
    piece.position = move.newPosition;
    piece.trackIndex = move.newTrackIndex;
  } else if (move.type === 'move') {
    piece.position = move.newPosition;
    piece.trackIndex = move.newTrackIndex;
    
    // Update state based on track position
    if (move.newTrackIndex >= 52) {
      piece.state = PIECE_STATES.IN_HOME_STRETCH;
    }
    
    // Check for captures
    if (!isSafePosition(move.newPosition[0], move.newPosition[1])) {
      // Check if any opponent pieces are on this position
      Object.keys(newGameState.pieces).forEach(otherPlayer => {
        if (otherPlayer !== player) {
          newGameState.pieces[otherPlayer].forEach(otherPiece => {
            if (otherPiece.position && 
                otherPiece.position[0] === move.newPosition[0] && 
                otherPiece.position[1] === move.newPosition[1]) {
              // Capture the piece - send it home
              otherPiece.state = PIECE_STATES.HOME;
              otherPiece.position = null;
              otherPiece.trackIndex = -1;
            }
          });
        }
      });
    }
  } else if (move.type === 'finish') {
    piece.state = PIECE_STATES.FINISHED;
    piece.position = null;
    piece.trackIndex = move.newTrackIndex;
  }

  return newGameState;
};

// Check if player has won
export const checkWinner = (gameState, player) => {
  const pieces = gameState.pieces[player];
  return pieces.every(piece => piece.state === PIECE_STATES.FINISHED);
};

// Check if player gets another turn (rolled 6 or captured)
export const getAnotherTurn = (diceRoll, capturedPiece) => {
  return diceRoll === 6 || capturedPiece;
};

// Get next player
export const getNextPlayer = (gameState) => {
  const nextIndex = (gameState.currentPlayerIndex + 1) % gameState.players.length;
  return {
    ...gameState,
    currentPlayerIndex: nextIndex
  };
};
